import { create } from 'zustand';

export interface StepAnalytics {
  stepId: string;
  stepName: string;
  views: number;
  completions: number;
  exits: number;
  dropOffRate: number;
}

export interface FormAnalytics {
  formId: string;
  totalViews: number;
  totalSubmissions: number;
  conversionRate: number;
  stepAnalytics: StepAnalytics[];
}

interface AnalyticsState {
  formAnalytics: Record<string, FormAnalytics>;
  getFormAnalytics: (formId: string) => FormAnalytics | null;
}

// Mock data for demonstration
const MOCK_ANALYTICS: Record<string, FormAnalytics> = {
  'test-highlevel': {
    formId: 'test-highlevel',
    totalViews: 248,
    totalSubmissions: 15,
    conversionRate: 6.05,
    stepAnalytics: [
      {
        stepId: 's1',
        stepName: 'Contact Information',
        views: 248,
        completions: 84,
        exits: 164,
        dropOffRate: 66.13
      },
      {
        stepId: 's2',
        stepName: 'Additional Information',
        views: 84,
        completions: 15,
        exits: 69,
        dropOffRate: 82.14
      }
    ]
  }
};

export const useAnalyticsStore = create<AnalyticsState>((set, get) => ({
  formAnalytics: MOCK_ANALYTICS,
  
  getFormAnalytics: (formId) => {
    return get().formAnalytics[formId] || null;
  }
}));