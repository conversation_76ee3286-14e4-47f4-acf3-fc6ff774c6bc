import { create } from 'zustand';

export interface StepAnalytics {
  stepId: string;
  stepName: string;
  views: number;
  completions: number;
  exits: number;
  dropOffRate: number;
}

export interface FormAnalytics {
  formId: string;
  totalViews: number;
  totalSubmissions: number;
  conversionRate: number;
  stepAnalytics: StepAnalytics[];
}

interface AnalyticsState {
  formAnalytics: Record<string, FormAnalytics>;
  getFormAnalytics: (formId: string) => FormAnalytics | null;
}

// Mock data for demonstration
const MOCK_ANALYTICS: Record<string, FormAnalytics> = {
  '1': {
    formId: '1',
    totalViews: 248,
    totalSubmissions: 15,
    conversionRate: 6.05,
    stepAnalytics: [
      {
        stepId: 's1',
        stepName: 'Basic Information',
        views: 248,
        completions: 84,
        exits: 164,
        dropOffRate: 66.13
      },
      {
        stepId: 's2',
        stepName: 'Service Rating',
        views: 84,
        completions: 41,
        exits: 43,
        dropOffRate: 51.19
      },
      {
        stepId: 's3',
        stepName: 'Additional Feedback',
        views: 41,
        completions: 26,
        exits: 15,
        dropOffRate: 36.59
      },
      {
        stepId: 's4',
        stepName: 'Contact Details',
        views: 26,
        completions: 15,
        exits: 11,
        dropOffRate: 42.31
      }
    ]
  }
};

export const useAnalyticsStore = create<AnalyticsState>((set, get) => ({
  formAnalytics: MOCK_ANALYTICS,
  
  getFormAnalytics: (formId) => {
    return get().formAnalytics[formId] || null;
  }
}));