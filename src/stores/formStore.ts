import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';

export type QuestionType = 
  | 'text'
  | 'textarea'
  | 'radio'
  | 'checkbox'
  | 'select'
  | 'image-select'
  | 'date';

export interface Choice {
  id: string;
  value: string;
  label: string;
  imageUrl?: string;
}

export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  placeholder?: string;
  helpText?: string;
  required: boolean;
  choices?: Choice[];
  conditionalLogic?: {
    questionId: string;
    operator: 'equals' | 'not_equals';
    value: string;
  };
}

export interface FormStep {
  id: string;
  title: string;
  description?: string;
  questions: Question[];
}

export interface Form {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  steps: FormStep[];
  settings: {
    primaryColor: string;
    logoUrl?: string;
    redirectUrl?: string;
    showProgressBar: boolean;
    thankYouMessage: string;
  };
}

interface FormState {
  forms: Form[];
  currentForm: Form | null;
  createForm: (name: string, description?: string) => string;
  updateForm: (form: Form) => void;
  deleteForm: (id: string) => void;
  getForm: (id: string) => Form | null;
  setCurrentForm: (id: string | null) => void;
  addStep: (formId: string) => void;
  updateStep: (formId: string, step: FormStep) => void;
  deleteStep: (formId: string, stepId: string) => void;
  addQuestion: (formId: string, stepId: string, type: QuestionType) => void;
  updateQuestion: (formId: string, stepId: string, question: Question) => void;
  deleteQuestion: (formId: string, stepId: string, questionId: string) => void;
}

const TEST_FORM: Form = {
  id: 'test-highlevel',
  name: 'HighLevel Test Form',
  description: 'Testing HighLevel integration',
  createdAt: new Date(),
  updatedAt: new Date(),
  steps: [
    {
      id: 's1',
      title: 'Contact Information',
      description: 'Please provide your contact details',
      questions: [
        {
          id: 'q1',
          type: 'text',
          title: 'Full Name',
          placeholder: 'John Doe',
          required: true,
        },
        {
          id: 'q2',
          type: 'text',
          title: 'Email Address',
          placeholder: '<EMAIL>',
          required: true,
        },
        {
          id: 'q3',
          type: 'text',
          title: 'Phone Number',
          placeholder: '+****************',
          required: true,
        }
      ],
    },
    {
      id: 's2',
      title: 'Additional Information',
      questions: [
        {
          id: 'q4',
          type: 'select',
          title: 'Interested Service',
          required: true,
          choices: [
            { id: 'c1', value: 'consultation', label: 'Free Consultation' },
            { id: 'c2', value: 'audit', label: 'Business Audit' },
            { id: 'c3', value: 'coaching', label: 'Business Coaching' }
          ]
        },
        {
          id: 'q5',
          type: 'textarea',
          title: 'Message',
          placeholder: 'Tell us more about your needs...',
          required: false,
        }
      ],
    }
  ],
  settings: {
    primaryColor: '#3B82F6',
    showProgressBar: true,
    thankYouMessage: 'Thanks! We\'ll be in touch soon.',
  }
};

const MOCK_FORMS: Form[] = [TEST_FORM];

export const useFormStore = create<FormState>()(
  persist(
    (set, get) => ({
      forms: MOCK_FORMS,
      currentForm: null,

  createForm: (name, description) => {
    const id = uuidv4();
    const newForm: Form = {
      id,
      name,
      description,
      createdAt: new Date(),
      updatedAt: new Date(),
      steps: [
        {
          id: uuidv4(),
          title: 'Step 1',
          questions: [],
        }
      ],
      settings: {
        primaryColor: '#3B82F6',
        showProgressBar: true,
        thankYouMessage: 'Thank you for your submission!',
      }
    };

    set(state => ({
      forms: [...state.forms, newForm],
      currentForm: newForm,
    }));

    return id;
  },

  updateForm: (form) => {
    set(state => ({
      forms: state.forms.map(f => f.id === form.id ? { ...form, updatedAt: new Date() } : f),
      currentForm: state.currentForm?.id === form.id ? { ...form, updatedAt: new Date() } : state.currentForm,
    }));
  },

  deleteForm: (id) => {
    set(state => ({
      forms: state.forms.filter(f => f.id !== id),
      currentForm: state.currentForm?.id === id ? null : state.currentForm,
    }));
  },

  getForm: (id) => {
    return get().forms.find(f => f.id === id) || null;
  },

  setCurrentForm: (id) => {
    if (id === null) {
      set({ currentForm: null });
      return;
    }

    const form = get().forms.find(f => f.id === id) || null;
    set({ currentForm: form });
  },

  addStep: (formId) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const newStep: FormStep = {
      id: uuidv4(),
      title: `Step ${form.steps.length + 1}`,
      questions: [],
    };

    const updatedForm = {
      ...form,
      steps: [...form.steps, newStep],
      updatedAt: new Date(),
    };

    set(state => ({
      forms: state.forms.map(f => f.id === formId ? updatedForm : f),
      currentForm: state.currentForm?.id === formId ? updatedForm : state.currentForm,
    }));
  },

  updateStep: (formId, step) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const updatedForm = {
      ...form,
      steps: form.steps.map(s => s.id === step.id ? step : s),
      updatedAt: new Date(),
    };

    set(state => ({
      forms: state.forms.map(f => f.id === formId ? updatedForm : f),
      currentForm: state.currentForm?.id === formId ? updatedForm : state.currentForm,
    }));
  },

  deleteStep: (formId, stepId) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form || form.steps.length <= 1) return; // Don't delete the last step

    const updatedForm = {
      ...form,
      steps: form.steps.filter(s => s.id !== stepId),
      updatedAt: new Date(),
    };

    set(state => ({
      forms: state.forms.map(f => f.id === formId ? updatedForm : f),
      currentForm: state.currentForm?.id === formId ? updatedForm : state.currentForm,
    }));
  },

  addQuestion: (formId, stepId, type) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const stepIndex = form.steps.findIndex(s => s.id === stepId);
    if (stepIndex === -1) return;

    const newQuestion: Question = {
      id: uuidv4(),
      type,
      title: 'New Question',
      required: false,
    };

    // Add default choices for choice-based questions
    if (['radio', 'checkbox', 'select', 'image-select'].includes(type)) {
      newQuestion.choices = [
        { id: uuidv4(), value: 'option1', label: 'Option 1' },
        { id: uuidv4(), value: 'option2', label: 'Option 2' },
      ];

      if (type === 'image-select') {
        newQuestion.choices.forEach(choice => {
          choice.imageUrl = 'https://via.placeholder.com/150';
        });
      }
    }

    const updatedSteps = [...form.steps];
    updatedSteps[stepIndex] = {
      ...updatedSteps[stepIndex],
      questions: [...updatedSteps[stepIndex].questions, newQuestion],
    };

    const updatedForm = {
      ...form,
      steps: updatedSteps,
      updatedAt: new Date(),
    };

    set(state => ({
      forms: state.forms.map(f => f.id === formId ? updatedForm : f),
      currentForm: state.currentForm?.id === formId ? updatedForm : state.currentForm,
    }));
  },

  updateQuestion: (formId, stepId, question) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const stepIndex = form.steps.findIndex(s => s.id === stepId);
    if (stepIndex === -1) return;

    const updatedSteps = [...form.steps];
    updatedSteps[stepIndex] = {
      ...updatedSteps[stepIndex],
      questions: updatedSteps[stepIndex].questions.map(q => 
        q.id === question.id ? question : q
      ),
    };

    const updatedForm = {
      ...form,
      steps: updatedSteps,
      updatedAt: new Date(),
    };

    set(state => ({
      forms: state.forms.map(f => f.id === formId ? updatedForm : f),
      currentForm: state.currentForm?.id === formId ? updatedForm : state.currentForm,
    }));
  },

  deleteQuestion: (formId, stepId, questionId) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const stepIndex = form.steps.findIndex(s => s.id === stepId);
    if (stepIndex === -1) return;

    const updatedSteps = [...form.steps];
    updatedSteps[stepIndex] = {
      ...updatedSteps[stepIndex],
      questions: updatedSteps[stepIndex].questions.filter(q => q.id !== questionId),
    };

    const updatedForm = {
      ...form,
      steps: updatedSteps,
      updatedAt: new Date(),
    };

    set(state => ({
      forms: state.forms.map(f => f.id === formId ? updatedForm : f),
      currentForm: state.currentForm?.id === formId ? updatedForm : state.currentForm,
    }));
  },
    }),
    {
      name: 'forms-storage', // localStorage key
      partialize: (state) => ({
        forms: state.forms,
        currentForm: state.currentForm
      }), // Persist forms and current form
      onRehydrateStorage: () => (state) => {
        // Convert string dates back to Date objects when loading from localStorage
        if (state?.forms) {
          state.forms = state.forms.map(form => ({
            ...form,
            createdAt: new Date(form.createdAt),
            updatedAt: new Date(form.updatedAt)
          }));
        }
        if (state?.currentForm) {
          state.currentForm = {
            ...state.currentForm,
            createdAt: new Date(state.currentForm.createdAt),
            updatedAt: new Date(state.currentForm.updatedAt)
          };
        }
      },
    }
  )
);