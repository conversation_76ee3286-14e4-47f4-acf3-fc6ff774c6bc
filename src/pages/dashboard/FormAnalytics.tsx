import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  Bar<PERSON>hart2, 
  ArrowLeft, 
  ChevronDown, 
  Calendar,
  Download 
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Cell
} from 'recharts';
import { useFormStore } from '../../stores/formStore';
import { useAnalyticsStore, StepAnalytics } from '../../stores/analyticsStore';

const FormAnalytics = () => {
  const { formId } = useParams<{ formId: string }>();
  const { getForm } = useFormStore();
  const { getFormAnalytics } = useAnalyticsStore();
  
  const form = formId ? getForm(formId) : null;
  const analytics = formId ? getFormAnalytics(formId) : null;
  
  const [dateRange, setDateRange] = useState('last30Days');
  const [showDateRangeDropdown, setShowDateRangeDropdown] = useState(false);
  
  const dateRangeOptions = [
    { value: 'last7Days', label: 'Last 7 days' },
    { value: 'last30Days', label: 'Last 30 days' },
    { value: 'lastQuarter', label: 'Last quarter' },
    { value: 'lastYear', label: 'Last year' },
  ];

  useEffect(() => {
    if (!form || !formId) {
      // Redirect or show error
    }
  }, [form, formId]);

  if (!form || !analytics) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <BarChart2 className="h-10 w-10 text-slate-400 mx-auto mb-2" />
          <h3 className="text-lg font-medium text-slate-900">Form not found</h3>
          <p className="text-slate-500 mb-4">The form you're looking for doesn't exist or you don't have access to it.</p>
          <Link to="/dashboard/analytics" className="btn-primary">
            Back to Analytics
          </Link>
        </div>
      </div>
    );
  }

  const getDateRangeLabel = () => {
    return dateRangeOptions.find(option => option.value === dateRange)?.label || 'Select date range';
  };

  const chartData = analytics.stepAnalytics.map((step) => ({
    name: step.stepName,
    dropOffRate: step.dropOffRate,
  }));

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Link to="/dashboard/analytics" className="p-2 text-slate-500 hover:text-slate-900 rounded-full hover:bg-slate-100">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold text-slate-900">{form.name} Analytics</h1>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div className="relative">
          <button 
            className="btn-secondary"
            onClick={() => setShowDateRangeDropdown(!showDateRangeDropdown)}
          >
            <Calendar className="h-4 w-4 mr-2" />
            {getDateRangeLabel()}
            <ChevronDown className="h-4 w-4 ml-2" />
          </button>
          
          {showDateRangeDropdown && (
            <div className="absolute z-10 mt-1 w-56 bg-white shadow-lg rounded-lg border border-slate-200 py-1">
              {dateRangeOptions.map((option) => (
                <button
                  key={option.value}
                  className={`w-full text-left px-4 py-2 text-sm hover:bg-slate-50 ${
                    dateRange === option.value ? 'font-medium text-primary-600 bg-primary-50' : 'text-slate-700'
                  }`}
                  onClick={() => {
                    setDateRange(option.value);
                    setShowDateRangeDropdown(false);
                  }}
                >
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
        
        <button className="btn-secondary">
          <Download className="h-4 w-4 mr-2" />
          Export data
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="card p-6">
          <h3 className="text-sm font-medium text-slate-500">Total Views</h3>
          <p className="mt-2 text-3xl font-semibold text-slate-900">{analytics.totalViews}</p>
          <div className="mt-1 text-sm text-slate-500">
            Users who viewed the form
          </div>
        </div>
        
        <div className="card p-6">
          <h3 className="text-sm font-medium text-slate-500">Submissions</h3>
          <p className="mt-2 text-3xl font-semibold text-slate-900">{analytics.totalSubmissions}</p>
          <div className="mt-1 text-sm text-slate-500">
            Completed form submissions
          </div>
        </div>
        
        <div className="card p-6">
          <h3 className="text-sm font-medium text-slate-500">Conversion Rate</h3>
          <p className="mt-2 text-3xl font-semibold text-slate-900">{analytics.conversionRate}%</p>
          <div className="mt-1 text-sm text-slate-500">
            Percentage of views that convert
          </div>
        </div>
      </div>

      <div className="card overflow-hidden">
        <div className="p-5 border-b">
          <h3 className="font-semibold text-slate-900">Form Funnel Analysis</h3>
          <p className="text-sm text-slate-500">Identify where users drop off in your form</p>
        </div>
        
        <div className="p-5">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="name" />
                <YAxis 
                  tickFormatter={(value) => `${value}%`}
                  domain={[0, 100]}
                />
                <Tooltip 
                  formatter={(value) => [`${value}%`, 'Drop-off Rate']}
                  labelStyle={{ fontWeight: 'bold' }}
                  contentStyle={{ 
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '0.5rem',
                    padding: '0.5rem'
                  }}
                />
                <Bar dataKey="dropOffRate" fill="#3B82F6" radius={[4, 4, 0, 0]}>
                  {chartData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.dropOffRate > 50 ? '#EF4444' : entry.dropOffRate > 30 ? '#F59E0B' : '#3B82F6'} 
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-slate-200">
            <thead className="bg-slate-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Step
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Views
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Completions
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Exits
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Drop-off Rate
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-slate-200">
              {analytics.stepAnalytics.map((step: StepAnalytics) => (
                <tr key={step.stepId} className="hover:bg-slate-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">
                    {step.stepName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {step.views}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {step.completions}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {step.exits}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span 
                        className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${
                          step.dropOffRate > 50 
                            ? 'bg-error-100 text-error-800' 
                            : step.dropOffRate > 30 
                              ? 'bg-warning-100 text-warning-800' 
                              : 'bg-primary-100 text-primary-800'
                        }`}
                      >
                        {step.dropOffRate}%
                      </span>
                      <div className="ml-2 flex-1 max-w-xs">
                        <div className="w-full bg-slate-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              step.dropOffRate > 50 
                                ? 'bg-error-500' 
                                : step.dropOffRate > 30 
                                  ? 'bg-warning-500' 
                                  : 'bg-primary-500'
                            }`}
                            style={{ width: `${step.dropOffRate}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FormAnalytics;